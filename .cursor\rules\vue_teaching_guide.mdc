---
description: 
globs: 
alwaysApply: true
---
# 回答用户不要使用表格 看不清楚  

# Vue 基础教学指南



## 目录结构与学习路径


vue_basic 目录包含了循序渐进的 Vue 学习案例：

- **入门基础**:
  - [01_初识Vue](mdc:vue_basic/01_初识Vue): Vue 的基本概念、创建实例
  - [02_Vue模板语法](mdc:vue_basic/02_Vue模板语法): 插值语法、指令语法
  - [03_数据绑定](mdc:vue_basic/03_数据绑定): 单向绑定、双向绑定
  - [04_el与data的两种写法](mdc:vue_basic/04_el与data的两种写法): 不同的初始化方式
  - [05_MVVM模型](mdc:vue_basic/05_MVVM模型): Vue 的设计思想和架构模式

- **核心概念**:
  - [06_数据代理](mdc:vue_basic/06_数据代理): Vue 如何代理数据实现响应式
  - [07_事件处理](mdc:vue_basic/07_事件处理): 事件监听、传参、修饰符等
  - [08_计算属性](mdc:vue_basic/08_计算属性): 计算属性的使用和缓存机制
  - [09_监视属性](mdc:vue_basic/09_监视属性): watch 的使用方法和深度监视
  - [10_绑定样式](mdc:vue_basic/10_绑定样式): class 和 style 绑定

- **条件与循环**:
  - [11_条件渲染](mdc:vue_basic/11_条件渲染): v-if、v-else、v-show 等
  - [12_列表渲染](mdc:vue_basic/12_列表渲染): v-for、key 的重要性
  - [13_收集表单数据](mdc:vue_basic/13_收集表单数据): 表单元素的值绑定和处理

- **进阶特性**:
  - [14_过滤器](mdc:vue_basic/14_过滤器): 文本格式化
  - [15_内置指令](mdc:vue_basic/15_内置指令): v-text、v-html、v-cloak 等
  - [16_自定义指令](mdc:vue_basic/16_自定义指令): 创建特定功能的指令
  - [17_生命周期](mdc:vue_basic/17_生命周期): Vue 实例的生命周期钩子

- **组件化开发**:
  - [18_非单文件组件](mdc:vue_basic/18_非单文件组件): 传统方式定义组件
  - [19_单文件组件](mdc:vue_basic/19_单文件组件): .vue 文件的使用

## 教学要点

### 通俗易懂的概念讲解
- 将Vue比作"智能助手"，自动帮你更新网页，不需手动操作DOM
- 响应式数据像"魔法便利贴"，内容变化时相关显示自动更新
- MVVM模式如同"餐厅服务流程"：顾客(View)、服务员(ViewModel)、厨师(Model)

### 示例代码剖析
- 每个示例先展示效果，让学生明白"做什么"
- 逐行讲解代码，解释"为什么这样写"和"背后发生了什么"
- 修改代码展示不同效果，理解代码与结果的关系

### HTML/CSS/JS基础关联
- 讲解Vue如何简化DOM操作，对比原生JS实现同样功能的代码量
- 解释CSS与Vue样式绑定的关系，如class绑定、style绑定
- 说明Vue如何增强HTML，让静态标记语言具备动态能力

### 浏览器工作原理结合
- 解释Virtual DOM如何提高性能，减少实际DOM操作
- 讲解浏览器渲染流程与Vue更新视图的关系
- 分析开发者工具中Vue组件渲染情况

### 实践指导与常见问题
- 数据不更新？检查数据是否响应式，是否正确使用v-model
- 列表渲染问题？强调key的重要性，不使用索引作key的原因
- 组件通信混乱？介绍单向数据流，props向下、事件向上的规则

### 学习路径引导
- 先掌握模板语法和数据绑定这些基础，再学习条件渲染和列表渲染
- 理解生命周期钩子后，才能更好地处理异步操作和DOM引用
- 组件化思想是Vue的核心，需重点掌握组件通信方式

### 思考题设计
- "如果要让用户输入自动格式化为大写，用计算属性还是侦听属性更合适？为什么？"
- "v-if和v-show的本质区别是什么？各适用什么场景？"
- "为什么Vue中不推荐直接操作DOM？什么情况下必须使用$refs操作DOM？"