<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>index</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.0.0-alpha.6/css/bootstrap.min.css" rel="stylesheet">
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <div class="col-md-12">
          <div class="carousel slide" id="carousel-283475">
            <ol class="carousel-indicators">
              <li data-slide-to="0" data-target="#carousel-283475">
              </li>
              <li data-slide-to="1" data-target="#carousel-283475">
              </li>
              <li data-slide-to="2" data-target="#carousel-283475" class="active">
              </li>
            </ol>
            <div class="carousel-inner">
              <div class="carousel-item">
                <img class="d-block w-100" alt="Carousel Bootstrap First"
                  src="http://www.atguigu.com/images/index_banner/iot-0712.jpg">
                <div class="carousel-caption">
                </div>
              </div>
              <div class="carousel-item">
                <img class="d-block w-100" alt="Carousel Bootstrap Second"
                  src="http://www.atguigu.com/images/index_banner/java2407.jpg">
                <div class="carousel-caption">
                </div>
              </div>
              <div class="carousel-item active">
                <img class="d-block w-100" alt="Carousel Bootstrap Third"
                  src="http://www.atguigu.com/images/index_banner/bigdata23.jpg">
                <div class="carousel-caption">
                </div>
              </div>
            </div> <a class="carousel-control-prev" href="#carousel-283475" data-slide="prev"><span
                class="carousel-control-prev-icon"></span> <span class="sr-only">Previous</span></a> <a
              class="carousel-control-next" href="#carousel-283475" data-slide="next"><span
                class="carousel-control-next-icon"></span> <span class="sr-only">Next</span></a>
          </div>
          <br>
          <button id="btn">提示轮播图数量</button>
        </div>
      </div>
    </div>
    <script src="./jQuery.js"></script>
    <script src="./tether.js"></script>
    <script src="./Bootstrap.js"></script>
    <script src="./user.js"></script>
  </body>
</html>