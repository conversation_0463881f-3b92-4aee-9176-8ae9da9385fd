# Vue 基础 - 列表渲染

## 目录

1. [基本列表](#基本列表)
2. [key 的原理与重要性](#key的原理与重要性)
   - [虚拟 DOM 工作原理](#虚拟dom工作原理)
   - [diff 算法详解](#diff算法详解)
   - [key 的实际作用](#key的实际作用)
3. [列表过滤](#列表过滤)
   - [使用 watch 实现](#使用watch实现)
   - [使用 computed 实现](#使用computed实现)
   - [两种实现方式对比](#两种实现方式对比)
4. [列表排序](#列表排序)
5. [更新时的问题](#更新时的问题)
   - [常见问题案例](#常见问题案例)
   - [解决方案](#解决方案)
6. [Vue 数据监测原理](#vue数据监测原理)
   - [对象的监测](#对象的监测)
   - [数组的监测](#数组的监测)
   - [深度监视原理](#深度监视原理)
   - [模拟实现数据监测](#模拟实现数据监测)
   - [Vue.set 的使用](#vueset的使用)
7. [列表渲染性能优化](#列表渲染性能优化)
   - [大数据列表优化](#大数据列表优化)
   - [不可变数据优化](#不可变数据优化)
8. [综合案例](#综合案例)
9. [总结](#总结)

## 基本列表

Vue 中使用`v-for`指令进行列表渲染。`v-for`指令基于一个数组来渲染一个列表，它有如下特点：

- 用于展示列表数据
- 语法：`v-for="(item, index) in xxx" :key="yyy"`
- 可遍历数组、对象、字符串和指定次数

示例：

```html
<!-- 遍历数组 -->
<ul>
  <li v-for="(p,index) of persons" :key="index">{{p.name}}-{{p.age}}</li>
</ul>

<!-- 遍历对象 -->
<ul>
  <li v-for="(value,k) of car" :key="k">{{k}}-{{value}}</li>
</ul>

<!-- 遍历字符串 -->
<ul>
  <li v-for="(char,index) of str" :key="index">{{char}}-{{index}}</li>
</ul>

<!-- 遍历指定次数 -->
<ul>
  <li v-for="(number,index) of 5" :key="index">{{index}}-{{number}}</li>
</ul>
```

**注意事项**：

- `v-for` 可以使用 `in` 或 `of` 作为分隔符，两者功能完全相同
- 遍历对象时可以指定三个参数：`v-for="(value, key, index) in object"`
- 遍历次数时，从 1 开始，如 `v-for="n in 5"` 会生成 1, 2, 3, 4, 5

## key 的原理与重要性

key 是虚拟 DOM 对象的标识，当数据发生变化时，Vue 会根据新数据生成新的虚拟 DOM，然后 Vue 进行新虚拟 DOM 与旧虚拟 DOM 的差异比较。

### 虚拟 DOM 工作原理

虚拟 DOM(Virtual DOM)是对真实 DOM 的轻量级 JavaScript 表示。当 Vue 组件初始化时：

1. 模板被编译成渲染函数
2. 渲染函数执行，创建虚拟 DOM 节点树
3. 虚拟 DOM 树被"挂载"，转换为实际 DOM 节点

当数据变化时：

1. 重新执行渲染函数，创建新的虚拟 DOM 树
2. 对比新旧虚拟 DOM 树（Diff 算法）
3. 计算出需要更新的最小 DOM 操作
4. 应用这些操作到实际 DOM

虚拟 DOM 结构示例：

```javascript
// 简化的虚拟DOM节点对象
{
  tag: 'div',
  key: 'unique-key',
  props: {
    className: 'container',
    style: { color: 'red' }
  },
  children: [
    { tag: 'span', children: '文本内容', key: 'child-1' },
    { tag: 'button', children: '点击', key: 'child-2' }
  ]
}
```

### diff 算法详解

Vue 的 diff 算法是基于以下策略：

1. **同层比较**：只比较同一层级的节点，不做跨层比较
2. **类型比较**：如果节点类型不同，直接替换整个节点（包括其子节点）
3. **key 值比较**：对于同类型节点，通过 key 判断节点是否可复用

具体 diff 过程：

1. 当两个节点 key 和类型都相同时：

   - 更新节点属性
   - 继续对比子节点

2. 对子节点列表的比较（使用 key 时）：
   - 使用"双端比较算法"找出可复用的节点
   - 为已有节点创建位置映射
   - 尽可能地移动和复用现有节点，而不是销毁重建

### key 的实际作用

当使用 key 时，Vue 可以精确识别节点身份，实现以下优化：

1. **精确复用**：找到具有相同 key 的节点并复用它，减少不必要的 DOM 操作
2. **高效移动**：当列表顺序变化时，只移动 DOM 元素而不是重新创建
3. **状态保留**：保持有状态组件的状态(如输入框的值、选中状态等)

### 对比规则

1. **旧虚拟 DOM 中找到了与新虚拟 DOM 相同的 key**

   - 若虚拟 DOM 中内容没变，直接使用之前的真实 DOM
   - 若虚拟 DOM 中内容变了，则生成新的真实 DOM，替换掉页面中之前的真实 DOM

2. **旧虚拟 DOM 中未找到与新虚拟 DOM 相同的 key**
   - 创建新的真实 DOM，渲染到页面

### 用 index 作为 key 可能引发的问题

1. **顺序操作问题**：若对数据进行逆序添加、逆序删除等破坏顺序操作，会产生没有必要的真实 DOM 更新，效率低

   ```javascript
   // 原数组
   [
     { id: 1, name: "张三" },
     { id: 2, name: "李四" },
   ][
     // 在前面添加元素后
     ({ id: 3, name: "王五" }, // 新元素
     { id: 1, name: "张三" },
     { id: 2, name: "李四" })
   ];
   ```

   使用 index 作为 key 时，原来的张三对应 key=0，现在变成 key=1，所有元素的 key 都发生变化，导致全部重新渲染。

2. **输入类 DOM 问题**：如果列表包含表单元素，且使用 index 作为 key，当插入/删除项目时会导致表单状态错误关联

   例如：一个列表中包含输入框，用户在第一个输入框中输入内容，然后在列表前插入一项，原来的输入内容会保留在第一个位置（对应新的数据），而不是跟随原数据移动。

### 如何选择 key?

1. 最好使用每条数据的唯一标识作为 key，如 id、手机号、身份证号、学号等唯一值
2. 如果不存在破坏顺序的操作，仅用于渲染列表展示，使用 index 作为 key 是没有问题的

## 列表过滤

Vue 中实现列表过滤有两种主要方式：监视属性(watch)和计算属性(computed)。

### 使用 watch 实现

```javascript
// 使用watch监视关键字变化，过滤列表
data: {
  keyWord: '',
  persons: [
    {id: '001', name: '马冬梅', age: 19, sex: '女'},
    {id: '002', name: '周冬雨', age: 20, sex: '女'},
    {id: '003', name: '周杰伦', age: 21, sex: '男'},
    {id: '004', name: '温兆伦', age: 22, sex: '男'}
  ],
  filPerons: []
},
watch: {
  keyWord: {
    immediate: true,
    handler(val) {
      this.filPerons = this.persons.filter((p) => {
        return p.name.indexOf(val) !== -1
      })
    }
  }
}
```

### 使用 computed 实现(推荐)

```javascript
// 使用computed实现过滤更简洁
data: {
  keyWord: '',
  persons: [
    {id: '001', name: '马冬梅', age: 19, sex: '女'},
    {id: '002', name: '周冬雨', age: 20, sex: '女'},
    {id: '003', name: '周杰伦', age: 21, sex: '男'},
    {id: '004', name: '温兆伦', age: 22, sex: '男'}
  ]
},
computed: {
  filPerons() {
    return this.persons.filter((p) => {
      return p.name.indexOf(this.keyWord) !== -1
    })
  }
}
```

### 两种实现方式对比

| 特性     | watch 方式                 | computed 方式                    |
| -------- | -------------------------- | -------------------------------- |
| 数据存储 | 需要额外的数组存储过滤结果 | 不需要额外的数组                 |
| 初始化   | 需要设置`immediate: true`  | 自动在创建时计算一次             |
| 性能     | 无缓存机制，每次都执行     | 有缓存机制，依赖不变时不重新计算 |
| 复杂度   | 代码较多                   | 代码简洁                         |
| 异步支持 | 支持异步操作               | 不支持异步操作                   |

**何时使用 watch 实现：**

- 需要在过滤过程中执行异步操作时
- 需要深度监听复杂数据结构时
- 过滤条件依赖多个变量且处理逻辑复杂时

**何时使用 computed 实现：**

- 简单的过滤逻辑
- 追求代码简洁和性能
- 不需要执行异步操作时

## 列表排序

结合过滤功能，我们还可以实现排序功能：

```javascript
data: {
  keyWord: '',
  sortType: 0, // 0原顺序 1降序 2升序
  persons: [
    {id: '001', name: '马冬梅', age: 30, sex: '女'},
    {id: '002', name: '周冬雨', age: 31, sex: '女'},
    {id: '003', name: '周杰伦', age: 18, sex: '男'},
    {id: '004', name: '温兆伦', age: 19, sex: '男'}
  ]
},
computed: {
  filPerons() {
    // 1.筛选
    const arr = this.persons.filter((p) => {
      return p.name.indexOf(this.keyWord) !== -1
    })

    // 2.排序
    if(this.sortType) {
      arr.sort((p1, p2) => {
        return this.sortType === 1
          ? p2.age - p1.age  // 降序
          : p1.age - p2.age  // 升序
      })
    }
    return arr
  }
}
```

完整 HTML 示例：

```html
<div id="root">
  <h2>人员列表</h2>
  <input type="text" placeholder="请输入名字" v-model="keyWord" />
  <button @click="sortType = 2">年龄升序</button>
  <button @click="sortType = 1">年龄降序</button>
  <button @click="sortType = 0">原顺序</button>
  <ul>
    <li v-for="(p,index) of filPerons" :key="p.id">
      {{p.name}}-{{p.age}}-{{p.sex}}
    </li>
  </ul>
</div>
```

排序类型：

- 0: 原始顺序
- 1: 年龄降序
- 2: 年龄升序

## 更新时的问题

Vue 在更新列表时可能遇到的问题主要与数据的响应式特性有关。Vue 不能检测以下变动的数组：

1. 当利用索引直接设置一个数组项时，例如：`vm.items[indexOfItem] = newValue`
2. 当修改数组的长度时，例如：`vm.items.length = newLength`

### 常见问题案例

```javascript
const vm = new Vue({
  data: {
    items: ["a", "b", "c"],
  },
});

// 以下操作不会触发视图更新
vm.items[1] = "x"; // 通过索引设置元素
vm.items.length = 2; // 修改数组长度
```

```javascript
const vm = new Vue({
  data: {
    userInfo: {
      name: "Tom",
      age: 18,
    },
  },
});

// 以下操作不会触发视图更新
vm.userInfo.gender = "male"; // 添加新属性
delete vm.userInfo.age; // 删除属性
```

### 解决方案

1. **对于数组的更新问题**：

   ```javascript
   // 方法1: 使用Vue.set / vm.$set
   Vue.set(vm.items, 1, "x");
   vm.$set(vm.items, 1, "x");

   // 方法2: 使用数组方法
   vm.items.splice(1, 1, "x");

   // 方法3: 使用新数组替换
   vm.items = [...vm.items.slice(0, 1), "x", ...vm.items.slice(2)];
   ```

2. **对于对象的更新问题**：

   ```javascript
   // 方法1: 使用Vue.set / vm.$set
   Vue.set(vm.userInfo, "gender", "male");
   vm.$set(vm.userInfo, "gender", "male");

   // 方法2: 使用新对象替换
   vm.userInfo = { ...vm.userInfo, gender: "male" };
   ```

## Vue 数据监测原理

### 对象的监测

Vue 通过`Object.defineProperty`方法来实现对象的响应式，为每个属性添加 getter 和 setter：

```javascript
// Vue如何监测对象中的数据
function observe(obj) {
  if (!obj || typeof obj !== "object") return;

  // 遍历对象的所有属性
  Object.keys(obj).forEach((key) => {
    defineReactive(obj, key, obj[key]);
  });
}

function defineReactive(obj, key, val) {
  // 递归观察子属性
  observe(val);

  // 创建依赖收集的容器
  const dep = new Dep();

  Object.defineProperty(obj, key, {
    get() {
      // 依赖收集
      if (Dep.target) {
        dep.depend();
      }
      return val;
    },
    set(newValue) {
      if (newValue === val) return;
      val = newValue;
      // 如果新值是对象，继续观察
      observe(newValue);
      // 通知更新
      dep.notify();
    },
  });
}
```

Vue 初始化时会递归遍历 data 中的所有属性，使用 Object.defineProperty 将它们转换为 getter/setter，这样在属性被访问或修改时能够触发相应的操作。

**监测局限性**：Vue 无法检测对象属性的添加或删除。

### 数组的监测

Vue 通过包装数组的变更方法（push、pop、shift、unshift、splice、sort、reverse）来实现对数组的监听：

```javascript
// Vue重写数组方法的简化实现
const arrayProto = Array.prototype;
const arrayMethods = Object.create(arrayProto);

["push", "pop", "shift", "unshift", "splice", "sort", "reverse"].forEach(
  (method) => {
    // 缓存原始方法
    const original = arrayProto[method];

    // 定义新方法
    Object.defineProperty(arrayMethods, method, {
      value: function (...args) {
        // 先调用原始方法
        const result = original.apply(this, args);

        // 获取数组的观察者对象
        const ob = this.__ob__;

        // 对于push, unshift, splice方法可能会添加新元素，需要进行观察
        let inserted;
        switch (method) {
          case "push":
          case "unshift":
            inserted = args;
            break;
          case "splice":
            inserted = args.slice(2);
            break;
        }

        if (inserted) ob.observeArray(inserted);

        // 通知更新
        ob.dep.notify();

        return result;
      },
      enumerable: false,
      writable: true,
      configurable: true,
    });
  }
);
```

Vue 通过原型链的方式，拦截数组的这些变异方法，在方法执行时执行原始操作，同时通知视图更新。

**数组响应式的局限**：

1. 不能直接通过索引修改数组元素触发更新：`arr[0] = newValue`
2. 不能直接修改数组长度触发更新：`arr.length = newLength`

### 深度监视原理

Vue 默认对对象进行深度监视，但对数组元素的监视需要特殊处理：

```javascript
function observe(value) {
  if (!isObject(value)) return;

  let ob;
  if (hasOwn(value, "__ob__") && value.__ob__ instanceof Observer) {
    ob = value.__ob__;
  } else {
    ob = new Observer(value);
  }
  return ob;
}

class Observer {
  constructor(value) {
    this.value = value;
    this.dep = new Dep();

    // 为值添加__ob__属性，指向Observer实例
    def(value, "__ob__", this);

    if (Array.isArray(value)) {
      // 覆盖数组原型方法
      value.__proto__ = arrayMethods;
      // 观察数组的每个元素
      this.observeArray(value);
    } else {
      // 遍历对象的每个属性，将其转换为getter/setter
      this.walk(value);
    }
  }

  // 遍历并定义对象的每个属性
  walk(obj) {
    const keys = Object.keys(obj);
    for (let i = 0; i < keys.length; i++) {
      defineReactive(obj, keys[i], obj[keys[i]]);
    }
  }

  // 观察数组的每个元素
  observeArray(arr) {
    for (let i = 0, l = arr.length; i < l; i++) {
      observe(arr[i]);
    }
  }
}
```

当启用深度监视（默认开启）时，Vue 会递归遍历对象的所有嵌套属性，将它们都转换为响应式的。

### 模拟实现数据监测

简化版的 Vue 数据监测实现：

```javascript
// 模拟Vue的响应式系统
function reactive(obj) {
  // 简化版，只处理对象
  if (typeof obj !== "object" || obj === null) return obj;

  // 遍历对象的所有属性
  Object.keys(obj).forEach((key) => {
    let value = obj[key];
    // 递归处理嵌套对象
    if (typeof value === "object" && value !== null) {
      value = reactive(value);
    }

    Object.defineProperty(obj, key, {
      get() {
        console.log(`访问了属性: ${key}`);
        return value;
      },
      set(newValue) {
        if (newValue === value) return;
        console.log(`属性${key}从${value}变成了${newValue}`);
        value = newValue;
        // 模拟更新视图
        render();
      },
    });
  });

  return obj;
}

// 模拟视图更新
function render() {
  console.log("视图已更新");
}

// 使用示例
const data = reactive({
  name: "张三",
  age: 18,
  info: {
    address: "北京",
  },
});

// 访问属性会触发getter
console.log(data.name);

// 修改属性会触发setter
data.age = 20;

// 修改嵌套属性也会触发setter
data.info.address = "上海";
```

### Vue.set 的使用

为了解决上述响应式的局限性，Vue 提供了`Vue.set`和`vm.$set`方法：

```javascript
// 为对象添加响应式属性
Vue.set(vm.student, "sex", "男");
// 或
vm.$set(vm.student, "sex", "男");

// 修改数组指定索引的值
Vue.set(vm.student.hobby, 0, "开车");
// 或
vm.$set(vm.student.hobby, 0, "开车");
```

Vue.set/vm.$set 的内部实现原理：

```javascript
function set(target, key, val) {
  // 处理数组
  if (Array.isArray(target)) {
    // 使用splice方法触发更新
    target.length = Math.max(target.length, key);
    target.splice(key, 1, val);
    return val;
  }

  // 如果key已经存在于target中
  if (key in target && !(key in Object.prototype)) {
    target[key] = val;
    return val;
  }

  // 获取target的Observer实例
  const ob = target.__ob__;

  // 如果target不是响应式对象，直接赋值
  if (!ob) {
    target[key] = val;
    return val;
  }

  // 将新属性定义为响应式
  defineReactive(target, key, val);

  // 通知更新
  ob.dep.notify();

  return val;
}
```

**注意**：`Vue.set`和`vm.$set`不能用于 Vue 实例或 Vue 实例的根数据对象。

## 列表渲染性能优化

### 大数据列表优化

当需要渲染大量数据时，可能会导致性能问题。以下是几种优化策略：

1. **分页渲染**：一次只渲染部分数据

```javascript
data() {
  return {
    allItems: Array(10000).fill().map((_, i) => ({ id: i, name: `Item ${i}` })),
    pageSize: 20,
    currentPage: 1
  }
},
computed: {
  displayItems() {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.allItems.slice(start, start + this.pageSize);
  }
}
```

2. **虚拟滚动**：只渲染可视区域的数据

```html
<!-- 使用第三方库如vue-virtual-scroller -->
<template>
  <RecycleScroller
    class="scroller"
    :items="items"
    :item-size="50"
    key-field="id"
    v-slot="{ item }"
  >
    <div class="user-item">{{ item.name }}</div>
  </RecycleScroller>
</template>
```

3. **延迟渲染**：使用 setTimeout 分批渲染大量数据

```javascript
data() {
  return {
    allItems: [],
    displayItems: [],
    chunkSize: 100
  }
},
methods: {
  loadData() {
    // 模拟加载10000条数据
    this.allItems = Array(10000).fill().map((_, i) => ({ id: i, name: `Item ${i}` }));
    this.renderByChunk();
  },
  renderByChunk() {
    if (this.allItems.length === 0) return;

    // 取出一部分数据进行渲染
    const chunk = this.allItems.splice(0, this.chunkSize);
    this.displayItems = [...this.displayItems, ...chunk];

    // 如果还有数据，继续渲染下一批
    if (this.allItems.length > 0) {
      setTimeout(() => {
        this.renderByChunk();
      }, 16); // 大约一帧的时间
    }
  }
}
```

### 不可变数据优化

使用不可变数据模式可以提高 Vue 列表渲染的性能：

1. **使用新数组替换旧数组**：

```javascript
// 不推荐
this.items[index].completed = true;

// 推荐
this.items = [
  ...this.items.slice(0, index),
  { ...this.items[index], completed: true },
  ...this.items.slice(index + 1),
];
```

2. **使用函数式组件**：对于纯展示的列表项，可以使用函数式组件降低开销

```javascript
Vue.component("item-component", {
  functional: true,
  props: ["item"],
  render(h, { props }) {
    return h("div", { class: "item" }, props.item.name);
  },
});
```

3. **使用 Object.freeze 冻结不变数据**：

```javascript
data() {
  return {
    // 冻结对象，Vue不会设置getter/setter
    staticData: Object.freeze([
      { id: 1, text: '永远不会变的数据1' },
      { id: 2, text: '永远不会变的数据2' }
    ])
  }
}
```

## 综合案例

下面是一个综合利用列表渲染、过滤、排序和 Vue.set 的完整案例：

```html
<div id="app">
  <div class="controls">
    <input v-model="searchText" placeholder="搜索姓名..." />
    <select v-model="filterGender">
      <option value="">全部性别</option>
      <option value="男">男</option>
      <option value="女">女</option>
    </select>
    <button @click="sortType = 0">原始顺序</button>
    <button @click="sortType = 1">年龄降序</button>
    <button @click="sortType = 2">年龄升序</button>
  </div>

  <table border="1">
    <thead>
      <tr>
        <th>ID</th>
        <th>姓名</th>
        <th>年龄</th>
        <th>性别</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(person, index) in filteredAndSortedPersons" :key="person.id">
        <td>{{ person.id }}</td>
        <td>{{ person.name }}</td>
        <td>{{ person.age }}</td>
        <td>{{ person.sex }}</td>
        <td>
          <button @click="addAge(person)">年龄+1</button>
          <button @click="addAttribute(person)">添加属性</button>
          <button @click="deletePerson(index)">删除</button>
        </td>
      </tr>
    </tbody>
  </table>

  <button @click="addPerson">添加人员</button>
</div>

<script>
  new Vue({
    el: "#app",
    data: {
      searchText: "",
      filterGender: "",
      sortType: 0,
      persons: [
        { id: "001", name: "马冬梅", age: 30, sex: "女" },
        { id: "002", name: "周冬雨", age: 31, sex: "女" },
        { id: "003", name: "周杰伦", age: 18, sex: "男" },
        { id: "004", name: "温兆伦", age: 19, sex: "男" },
      ],
      nextId: 5,
    },
    computed: {
      filteredAndSortedPersons() {
        // 1.筛选名字
        let result = this.persons.filter((p) => {
          return p.name.indexOf(this.searchText) !== -1;
        });

        // 2.筛选性别
        if (this.filterGender) {
          result = result.filter((p) => p.sex === this.filterGender);
        }

        // 3.排序
        if (this.sortType !== 0) {
          result.sort((p1, p2) => {
            return this.sortType === 1
              ? p2.age - p1.age // 降序
              : p1.age - p2.age; // 升序
          });
        }

        return result;
      },
    },
    methods: {
      addAge(person) {
        person.age++;
      },
      addAttribute(person) {
        // 使用Vue.set添加一个标记属性
        if (!person.hasOwnProperty("isVIP")) {
          this.$set(person, "isVIP", true);
          alert(`${person.name}已被设为VIP!`);
        } else {
          alert(`${person.name}已经是VIP了!`);
        }
      },
      deletePerson(index) {
        // 使用splice方法删除元素
        this.persons.splice(index, 1);
      },
      addPerson() {
        // 添加新人员
        const newId = `00${this.nextId}`;
        const newPerson = {
          id: newId,
          name: `新人员${this.nextId}`,
          age: Math.floor(Math.random() * 30 + 18),
          sex: Math.random() > 0.5 ? "男" : "女",
        };
        this.persons.unshift(newPerson);
        this.nextId++;
      },
    },
  });
</script>
```

## 总结

Vue 的列表渲染功能强大且灵活，关键点包括：

1. **v-for 基础**：可遍历数组、对象、字符串和指定次数
2. **key 的重要性**：
   - 是虚拟 DOM 的唯一标识
   - 正确使用 key 可以提高 DOM 更新的效率
   - 应优先使用数据的唯一标识作为 key
3. **过滤与排序**：
   - 可通过 computed 或 watch 实现
   - computed 更为简洁，有缓存机制
   - watch 适合处理异步过滤操作
4. **数据监测原理**：
   - Vue 通过 Object.defineProperty 实现响应式
   - 对象的响应式通过 getter/setter 实现
   - 数组的响应式通过重写变异方法实现
5. **数据更新限制**：
   - 直接修改数组索引或长度不会触发更新
   - 对象动态添加的属性不会自动成为响应式
6. **Vue.set 的使用**：
   - 用于向响应式对象添加新属性
   - 用于修改数组指定索引的值
7. **性能优化**：
   - 大数据列表使用虚拟滚动或分页
   - 使用不可变数据模式
   - 避免不必要的响应式转换

在 Vue 中修改数组时，应优先使用以下方法：

- 使用变异方法：push()、pop()、shift()、unshift()、splice()、sort()、reverse()
- 使用 Vue.set()或 vm.$set()
- 使用新数组替换旧数组

理解列表渲染原理和最佳实践，对于构建高效的 Vue 应用至关重要。特别是了解虚拟 DOM 和 diff 算法的工作原理，可以帮助我们编写更有性能的代码，避免常见的陷阱。
