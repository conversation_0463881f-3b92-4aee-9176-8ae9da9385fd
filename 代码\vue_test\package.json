{"name": "vue_test", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "^4.1.1", "axios": "^0.21.1", "babel-preset-es2015": "^6.24.1", "core-js": "^3.6.5", "element-ui": "^2.15.3", "less-loader": "^7.3.0", "nanoid": "^3.1.23", "pubsub-js": "^1.9.3", "vue": "^2.6.11", "vue-resource": "^1.5.3", "vue-router": "^3.5.2", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}